# BarChartService 柱状图数据服务功能文档

## 概述

BarChartService 是外挂检测系统中的柱状图数据服务类，主要用于统计和分析外挂检测相关的数据，为前端图表展示提供结构化的数据支持。

## 功能特性

### 核心功能
1. **风险等级分布统计** - 统计低、中、高风险的数据分布
2. **游戏上传类型分析** - 分析正常上传、重签包、AI检测的数据分布
3. **Root状态统计** - 统计设备Root状态分布
4. **重签包名统计** - 统计重签包的包名及出现频次

### 技术特性
- 基于StarRocks数据仓库的高性能查询
- 完整的异常处理和日志记录
- 结构化的数据格式输出
- 支持多维度数据筛选

## 类结构

### 常量定义

#### 游戏上传类型
```php
const GAME_UPLOAD_NORMAL = 1;    // 正常游戏上传
const GAME_UPLOAD_RESIGN = 2;    // 重签包上传  
const GAME_UPLOAD_AI = 3;        // AI检测上传
```

#### Root状态
```php
const ROOT_STATUS_YES = 1;       // 已Root
const ROOT_STATUS_NO = 0;        // 未Root
```

#### 风险等级
```php
const RISK_LEVEL_LOW = 1;        // 低风险
const RISK_LEVEL_MEDIUM = 2;     // 中风险
const RISK_LEVEL_HIGH = 3;       // 高风险
```

## 主要方法

### getData(): array
获取完整的柱状图数据，包含所有统计维度的信息。

**返回数据结构：**
```php
[
    'risk_and_upload_data' => [
        'risk_level' => [
            'low_risk' => 100,      // 低风险数量
            'medium_risk' => 50,    // 中风险数量
            'high_risk' => 20,      // 高风险数量
        ],
        'upload_type' => [
            'game_upload' => 120,   // 正常游戏上传数量
            'resign_count' => 30,   // 重签包上传数量
            'ai_count' => 20,       // AI检测上传数量
        ],
        'root_status' => [
            'is_root' => 80,        // 已Root设备数量
            'not_root' => 90,       // 未Root设备数量
        ],
    ],
    'sign_package_count' => [
        ['name' => 'com.example.app1', 'num' => 15],
        ['name' => 'com.example.app2', 'num' => 10],
        // ... 更多重签包统计
    ]
]
```

### getStatisticsData(): array
获取基础统计数据，包含风险等级、游戏上传类型、Root状态的原始统计信息。

### getSignPackageStatistics(): array
获取重签包名统计数据，按出现次数降序排列。

## 数据源

### 主要数据表
- **anti_chect_init** - 外挂检测初始化数据表
- **anti_chect_upload_data_primary_v2** - 外挂检测上传数据主表V2

### 数据关联
通过 `session_id` 字段关联两个表，获取完整的检测数据信息。

## 使用示例

### 基础用法
```php
// 初始化服务（需要传入筛选参数）
$params = [
    'start_date' => '2024-01-01',
    'end_date' => '2024-01-31',
    'extra_app_id' => 12345
];

$service = new BarChartService($params);

// 获取柱状图数据
try {
    $data = $service->getData();
    
    // 处理返回数据
    $riskData = $data['risk_and_upload_data']['risk_level'];
    $uploadData = $data['risk_and_upload_data']['upload_type'];
    $rootData = $data['risk_and_upload_data']['root_status'];
    $packageData = $data['sign_package_count'];
    
} catch (Exception $e) {
    // 处理异常
    Log::error('获取柱状图数据失败: ' . $e->getMessage());
}
```

### 高级用法
```php
// 带更多筛选条件
$params = [
    'start_date' => '2024-01-01',
    'end_date' => '2024-01-31',
    'extra_app_id' => 12345,
    'account_id' => [1001, 1002, 1003],
    'server_id' => [1, 2],
    'risk_level' => [2, 3]  // 只查询中高风险
];

$service = new BarChartService($params);
$data = $service->getData();
```

## 性能优化

### 查询优化
- 使用单次SQL查询获取多维度统计数据
- 通过索引优化查询性能
- 合理使用CASE WHEN语句进行条件统计

### 缓存建议
- 可考虑对相同参数的查询结果进行缓存
- 建议缓存时间：5-10分钟
- 缓存键格式：`bar_chart_data_{md5(serialize($params))}`

## 异常处理

### 异常类型
1. **数据库连接异常** - StarRocks连接失败
2. **SQL执行异常** - 查询语句执行错误
3. **数据格式异常** - 返回数据格式不符合预期

### 日志记录
- 所有关键操作都有详细的日志记录
- 异常信息包含完整的错误堆栈
- 日志级别：INFO（正常操作）、ERROR（异常情况）

## 注意事项

1. **参数验证** - 使用前需要验证传入参数的有效性
2. **权限控制** - 需要确保调用者有相应的数据访问权限
3. **数据量控制** - 大数据量查询时注意性能影响
4. **时区处理** - 日期参数需要注意时区转换

## 版本历史

- **v1.0** (2025-06-12) - 初始版本，基础功能实现
- **v2.0** (2025-06-12) - 代码重构优化，增加异常处理和日志记录

## 相关文档

- [BaseService 基础服务文档](./BaseService功能文档.md)
- [外挂检测系统架构文档](./外挂检测系统架构.md)
- [API接口文档](./showdoc/柱状图数据API文档.md)
