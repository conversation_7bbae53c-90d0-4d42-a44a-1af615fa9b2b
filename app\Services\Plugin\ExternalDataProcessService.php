<?php

/**
 * 外挂数据处理服务类
 * @desc 外挂数据处理服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/01/09
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ExternalDataProcessService extends BaseService
{
    /**
     * 缓存前缀
     *
     * @var string
     */
    const CACHE_PREFIX = 'external_session_id_';

    /**
     * 缓存时间（2小时）
     *
     * @var int
     */
    const CACHE_TTL = 7200;

    /**
     * 处理外挂数据
     *
     * @param array $data
     * @return bool
     */
    public function processData(array $data): bool
    {
        try {
            // 1. 生成session_id
            $sessionId = $this->generateSessionId($data);
            // 2. 检查缓存，避免重复处理
            $cacheKey = self::CACHE_PREFIX . $sessionId;
            if (Cache::has($cacheKey)) {
                Log::info("外挂数据已处理，跳过, session_id: " . $sessionId);
                return true;
            }
            $riskLevel = 3;
            // 3. 写入初始化数据
            $this->writeExternalInitData($sessionId, $data);
            // 4. 写入上传数据
            $this->writeExternalUploadData($sessionId, $data, $riskLevel);
            // 5. 写入命中数据
            $this->writeExternalHitData($sessionId, $data, $riskLevel);
            // 6. 设置缓存
            Cache::put($cacheKey, 1, self::CACHE_TTL);
            // 7. 打印日志
            Log::info("外挂数据处理完成: session_id=" . $sessionId . " | risk_level=" . $riskLevel);
            return true;
        } catch (\Exception $e) {
            Log::error("外挂数据处理失败: " . $e->getMessage() . " | 堆栈跟踪: " . $e->getTraceAsString() . " | 数据: " . json_encode($data));
        }
        // 处理失败
        return false;
    }

    /**
     * 生成session_id
     *
     * @param array $data
     * @return string
     */
    protected function generateSessionId(array $data): string
    {
        $string = strtotime($data['created_time']) . $data['server_dev_str'] . $data['extra_app_id'] . $data['os_type'];
        return md5($string);
    }

    /**
     * 写入外挂初始化数据
     *
     * @param string $sessionId
     * @param array $data
     * @return void
     */
    public function writeExternalInitData(string $sessionId, array $data): void
    {
        $initData = [
            'android_id' => $data['server_dev_str'],
            'app_name' => $data['app_name'] ?? '',
            'app_package_name' => $data['sdk_package_name'] ?? '',
            'app_version' => $data['app_version'] ?? '',
            'cpu_framework' => $data['rom_info'] ?? '',
            'stream_date' => $data['created_time'],
            'device_name' => $data['manufacturer'] . ' ' . $data['device_model'],
            'device_brand' => $data['manufacturer'],
            'device_manufacturer' => $data['manufacturer'],
            'device_model' => $data['device_model'],
            'extra_app_id' => $data['extra_app_id'],
            'ip' => $data['ip'] ?? '',
            'is_emulator' => $data['is_emulator'] ?? '0',
            'is_root' => $data['is_root'] ?? '0',
            'network_type' => $data['network_type'] ?? '',
            'os_type' => $data['os_type'],
            'os_version' => $data['os_version'],
            'resolution' => str_replace('X', '*', $data['resolution'] ?? ''),
            'rom_info' => $data['rom_info'] ?? '',
            'sdk_ver' => $data['sdk_ver'] ?? '',
            'oaid' => $data['server_dev_str'],
            'server_dev_str' => $data['server_dev_str'],
            'session_id' => $sessionId,
            'slot_count' => '',
            'time_zone_id' => '',
            'ui_mode_type' => '',
            'vpn_address' => '',
            'app_process_name' => '',
            'app_sign_info' => '',
            'bluetooth_address' => '',
            'cpu_abis' => '',
            'cpu_cores' => '0',
            'cpu_cur' => '',
            'cpu_max' => '',
            'cpu_min' => '',
            'cpu_name' => '',
            'device_board' => '',
            'device_display' => '',
            'device_hardware' => '',
            'device_id' => '',
            'device_product' => '',
            'device_security_patch' => '',
            'device_serial_id' => '',
            'device_tags' => '',
            'device_time' => '',
            'device_type' => '',
            'dns' => '',
            'is_accessibility' => '0',
            'is_airplane_mode' => '0',
            'is_cloudPhone' => '0',
            'is_mock_location' => '0',
            'is_tablet' => '0',
            'is_use_debug' => '0',
            'is_using_vpn' => '0',
            'keyboard' => '0',
            'language' => '',
            'last_boot_time' => '',
            'locale_display_language' => '',
            'locale_iso_3_country' => '',
            'locale_iso_3_language' => '',
            'mac' => '',
            'mcc' => '',
            'mnc' => '',
            'network_operator' => '',
            'network_operator_name' => '',
            'phone_type' => '',
            'ringer_mode' => '2',
            'screen_density' => '',
            'screen_density_dpi' => '',
            'screen_physical_size' => '',
        ];

        $this->writeToFile('external-init-data', $initData);
    }

    /**
     * 写入外挂上传数据
     *
     * @param string $sessionId
     * @param array $data
     * @param int $riskLevel
     * @return void
     */
    public function writeExternalUploadData(string $sessionId, array $data, int $riskLevel): void
    {
        $uploadData = [
            'application_info' => [],
            'click_info' => '',
            'extra_app_id' => $data['extra_app_id'],
            'game_info' => json_encode([
                'account_id' => $data['account_id'] ?? '',
                'role_id' => $data['role_id'] ?? '',
                'role_name' => $data['role_name'] ?? '',
                'server_id' => $data['server_id'] ?? '',
                'server_name' => $data['server_name'] ?? '',
                'extension' => '',
            ], JSON_UNESCAPED_UNICODE),
            'ip' => $data['ip'] ?? '',
            'is_accessibility' => 0,
            'is_plugin' => $riskLevel == 3 ? 1 : 0,
            'network_type' => $data['network_type'] ?? '',
            'os_type' => $data['os_type'],
            'port_info' => '',
            'report_interval' => 0,
            'risk_level' => $riskLevel,
            'server_dev_str' => $data['server_dev_str'],
            'session_id' => $sessionId,
            'socket_info' => '',
            'stream_date' => $data['created_time'],
            'write_info' => '',
            'is_game_upload' => $data['is_game_upload'] ?? 0,
            'hitbug_explain_desc' => $data['description'] ?? '',
            'hitbug_exception_image' => '',
        ];

        $this->writeToFile('external-upload-data', $uploadData);
    }

    /**
     * 写入外挂命中数据
     *
     * @param string $sessionId
     * @param array $data
     * @param int $riskLevel
     * @return void
     */
    public function writeExternalHitData(string $sessionId, array $data, int $riskLevel): void
    {
        $hitData = [
            'session_id' => $sessionId,
            'stream_date' => $data['created_time'],
            'port_info' => [],
            'socket_info' => [],
            'click_info' => json_encode(['click_num' => 0, 'match_num' => 0, 'ratio' => 0], JSON_UNESCAPED_UNICODE),
            'action' => '99',
            'title' => '',
            'extra_app_id' => $data['extra_app_id'],
            'server_dev_str' => $data['server_dev_str'],
            'ip' => $data['ip'] ?? '',  // IP地址
            'account_id' => $data['account_id'] ?? '',
            'application_info' => '',
            'write_info' => '',
            'phone_info' => json_encode([
                'is_dpi' => 0,
                'is_emulator' => $data['is_emulator'] ?? '0',
                'is_resolution' => 0,
                'is_root' => $data['is_root'] ?? '0',
            ], JSON_UNESCAPED_UNICODE),
            'risk_level' => $riskLevel,
        ];

        $this->writeToFile('external-hit-data', $hitData);
    }

    /**
     * 写入文件
     *
     * @param string $type
     * @param array $data
     * @return void
     */
    protected function writeToFile(string $type, array $data): void
    {
        $date = date('Ymd');
        $path = "/data/www/developer/exception-manager/storage/app/{$type}/{$date}.log";

        // 判断目录是否存在，不存在就创建
        $dir = pathinfo($path)['dirname'];
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }

        // 追加的方式添加，每行一条数据
        file_put_contents($path, json_encode($data, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
}
