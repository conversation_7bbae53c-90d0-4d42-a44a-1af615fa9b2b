<?php

/**
 * 外挂检测报告详情
 * @desc 外挂检测报告详情
 * <AUTHOR> <EMAIL>
 * @date 2023/12/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use App\Models\StarRocks\AntiChectHitLog;
use App\Models\StarRocks\AntiChectHitLogDetail;
use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimary;
use App\Models\StarRocks\IpParse;
use App\Services\IpServer;

class DetailService extends BaseService
{
    /**
     * 获取游戏信息
     *
     * @return array
     */
    private function getGameInfo()
    {
        //获取游戏数据
        $gameInfo = AntiChectUploadDataPrimary::query()->where('session_id', $this->params['session_id'])->firstFromSR();
        if ($gameInfo) {
            $gameInfo['game_info'] = json_decode($gameInfo['game_info'], true) ?? [];
        }
        return $gameInfo;
    }

    /**
     * 获取地区信息
     *
     * @return string
     */
    private function getArea()
    {
        //获取ip解析服务类
        $ipServer = IpServer::getInstance();
        $ipInfo = $ipServer->getResultAll($this->params['ip']);
        if ($ipInfo) {
            return "{$ipInfo['country']}-{$ipInfo['province']}-{$ipInfo['city']}";
        }
        return '';
    }

    /**
     * 获取命中报告
     *
     * @return string
     */
    private function getHitReport()
    {
        // 获取列表中是否有命中风险的报告
        $highReport = AntiChectHitLog::query()
            ->where('session_id', $this->params['session_id'])
            ->firstFromSR();
        if ($highReport) {
            return $highReport;
        } else {
            return [];
        }
    }

    /**
     * 获取命中外挂时长
     *
     * @return int
     */
    private function getHitDuration()
    {
        //获取列表中是否有命中风险的报告
        $result = AntiChectHitLogDetail::query()
            ->selectRaw('COUNT(*) AS count')
            ->where('session_id', $this->params['session_id'])
            ->firstFromSR();
        if ($result) {
            return $result['count'];
        } else {
            return 0;
        }
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        $gameInfo = $this->getGameInfo();

        $hitReport = $this->getHitReport();

        // 风险标签
        $riskTag = $this->getRiskTag([
            'application_info' => $gameInfo["application_info"],
            'is_emulator' => $this->params["is_emulator"],
            'device_brand' => $this->params["device_brand"],
            'is_root' => $this->params["is_root"],
            'is_write' => $gameInfo['is_write'],
            'write_info' => $gameInfo['write_info'],
            'screen_density_dpi' => $this->params["screen_density_dpi"],
            'resolution' => $this->params["resolution"],
            'port_info' => $this->params['os_type'] == AntiChectInit::PC ? ($hitReport['port_info'] ?? []) : [],
        ]);

        // 证据处理
        $evidence = "";
        if ($this->params['os_type'] == AntiChectInit::PC) {
            $portInfo = json_decode($hitReport['port_info'] ?? '', true) ?? [];
            $evidence .= !empty($portInfo) ? 'DLL异常；' : "";
        } else {
            $portInfo = json_decode($hitReport['port_info'] ?? '', true) ?? [];
            $evidence .= !empty($portInfo) ? '进程异常；' : "";
        }
        $socketInfo = json_decode($hitReport['socket_info'] ?? '', true) ?? [];
        $evidence .= !empty($socketInfo) ? '套子异常；' : "";
        $clickInfo = json_decode($hitReport['click_info'] ?? '', true) ?? ['ratio' => 0];
        $evidence .= $clickInfo['ratio'] >= 80 ? '行为异常；' : "";
        $evidence = trim($evidence, '；');
        // 如果证据为空, 则展示上报类型
        if (empty($evidence)) {
            switch ($gameInfo['is_game_upload']) {
                case 1:
                    $evidence = "游戏上报";
                    break;
                case 2:
                    $evidence = "重签识别";
                    break;
                case 3:
                    $evidence = "AI识别";
                    break;
            }
        }
        // 获取上报类型
        $reportType = $gameInfo['is_game_upload'];
        if (empty($reportType)) {
            $reportType = '0';
        }

        // 获取命中外挂时长
        $hitDuration = $this->getHitDuration() * $gameInfo['report_interval'];
        // 获取报告时长，单位：秒，最后上报时间减去初始化时间
        $reportDuration = strtotime($gameInfo['stream_date']) - strtotime($this->params['stream_date']);
        // 非外挂时长
        $noHitDuration = $reportDuration - $hitDuration;

        return [
            'app_name' => $this->params['app_name'] ?? '', //应用名称
            'app_package_name' => $this->params['app_package_name'] ?? '', //应用包名
            'app_version' => $this->params['app_version'] ?? '', //应用版本号
            'stream_date' => $this->params['stream_date'] ?? '', //应用初始化时间
            'sdk_ver' => $this->params['sdk_ver'] ?? '', //SDK插件版本号
            'app_sign_info' => $this->params['app_sign_info'] ?? '', //应用MD值
            'resource_version' => '', //资源版本号
            'session_id' => $this->params['session_id'] ?? '', //session_id
            'network_type' => $this->params['network_type'] ?? '', //联网类型
            'ip_address' => $this->params['ip'] ?? '', //IP地址
            'area' => $this->getArea(), //地区
            'account_id' => $gameInfo['game_info']['account_id'] ?? '', //账号ID
            'role_id' => $gameInfo['game_info']['role_id'] ?? '', //角色ID
            'role_name' => $gameInfo['game_info']['role_name'] ?? '', //角色名称
            'role_level' => '', //角色等级
            'server_name' => $gameInfo['game_info']['server_name'] ?? '', //服务器名称
            'server_level' => '', //服务器等级
            'login_time' => [
                'plugin_time' => $hitDuration > $reportDuration ? $reportDuration : $hitDuration, //命中外挂时长
                'no_plugin_time' => $noHitDuration < 0 ? 0 : $noHitDuration, //非外挂时长
            ], //登录时长
            'server_dev_str' => $this->params['server_dev_str'] ?? '', //设备ID
            'os_type' => $this->params['os_type'] ?? '', //设备系统
            'device_name' => $this->params['device_name'] ?? '', //设备类型
            'device_brand' => $this->params['device_brand'] ?? '', //品牌
            'device_model' => $this->params['device_model'] ?? '', //型号
            'os_version' => $this->params['os_version'] ?? '', //系统版本
            'risk_level' => $hitReport ? static::RISK_LEVEL_MAP[intval($hitReport['risk_level'])] : (new CheckRiskService([
                'write_info' => $gameInfo['write_info'],
                'resolution' => $this->params['resolution'],
                'screen_density_dpi' => $this->params['screen_density_dpi'],
                'is_root' => $this->params['is_root'],
                'risk_level' => $gameInfo['risk_level'],
            ]))->getLevel(), //风险等级
            'action' => $hitReport['action'] ?? 0, //执行动作
            'risk_tag' => $riskTag, //风险标签
            'evidence' => $evidence, //证据展示
            'application_info' => json_decode($gameInfo['application_info'], true) ?? [], //应用信息
            'hitbug_explain_desc' => $gameInfo['hitbug_explain_desc'] ?? '', //外挂说明
            'hitbug_exception_image' => $gameInfo['hitbug_exception_image'] ?? '', //外挂截图
            'report_type' => $reportType, //上报类型
        ];
    }
}
