<?php

/**
 * 柱状图数据控制器类
 * @desc 柱状图数据控制器类，提供外挂检测数据的柱状图统计接口
 * <AUTHOR> chenji<PERSON><EMAIL>
 * @date 2025/06/12
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Http\Validation\OverviewValidation;
use App\Services\Plugin\BarChartService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class BarChartController extends Controller
{
    /**
     * 获取柱状图统计数据
     *
     * 提供外挂检测数据的多维度统计分析，包括：
     * 1. 风险等级分布统计（低、中、高风险）
     * 2. 游戏上传类型分布（正常上传、重签包、AI检测）
     * 3. Root状态分布（已Root、未Root）
     * 4. 重签包名统计（按出现频次排序）
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/xxxxx
     * @return array
     * @throws ValidationException
     */
    public function getData(): array
    {
        // 校验参数
        $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();

        try {
            // 创建服务实例并获取数据
            $service = new BarChartService($params);
            $result = $service->getData();

            // 返回成功响应
            return $this->success($result);
        } catch (Exception $e) {
            Log::error('BarChartController::getData 获取柱状图数据失败', [
                'error' => $e->getMessage(),
                'params' => $params,
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, '获取柱状图数据失败，请稍后重试');
        }
    }
}
