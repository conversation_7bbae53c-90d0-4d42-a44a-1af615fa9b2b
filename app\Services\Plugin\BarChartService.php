<?php

/**
 * 柱状图数据服务类
 * @desc 柱状图数据服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/06/12
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimaryV2;

class BarChartService extends BaseService
{
    /**
     * 获取柱状图数据
     *
     * @return array
     */
    public function getData(): array
    {
        $riskLevelAndIsGameUploadCount = $this->getRiskLevelAndIsGameUploadCount();
        $hitbugExplainDescCount = $this->getHitbugExplainDescCount();
        return [
            'riskLevelAndIsGameUploadCount' => [
                'riskLevelCount' => [
                    'lowRiskCount' => $riskLevelAndIsGameUploadCount['risk_level_1_count'] ?? 0,
                    'mediumRiskCount' => $riskLevelAndIsGameUploadCount['risk_level_2_count'] ?? 0,
                    'highRiskCount' => $riskLevelAndIsGameUploadCount['risk_level_3_count'] ?? 0,
                ],
                'isGameUploadCount' => [
                    
            ],
            'signPackageNameCount' => $hitbugExplainDescCount
        ];
    }

    /**
     * 获取重签包名数量
     * 
     * @return array
     */
    public function getHitbugExplainDescCount()
    {
        $builder = $this->getInitJoinPrimaryBuilder();
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $selectRaw = <<<SELECT
anti_chect_upload_data_primary_v2.hitbug_explain_desc as name,
COUNT(1) as num
SELECT;
        return $builder->selectRaw($selectRaw)
            ->where($table . '.is_game_upload', 2)
            ->where($table . '.hitbug_explain_desc', '!=', '')
            ->groupBy($table . '.hitbug_explain_desc')
            ->orderBy('num', 'desc')
            ->getFromSR();
    }

    /**
     * 获取风险等级、检测类型分布、是否root
     *
     * @return array
     */
    public function getRiskLevelAndIsGameUploadCount()
    {
        $builder = $this->getInitJoinPrimaryBuilder();
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $initTable = AntiChectInit::TABLE_NAME;

        $selectRaw = <<<SELECT
SUM(
    CASE
        WHEN {$table}.risk_level = 1 THEN 1
        ELSE 0
    END
) AS risk_level_1_count,
SUM(
    CASE
        WHEN {$table}.risk_level = 2 THEN 1
        ELSE 0
    END
) AS risk_level_2_count,
SUM(
    CASE
        WHEN {$table}.risk_level = 3 THEN 1
        ELSE 0
    END
) AS risk_level_3_count,
SUM(
    CASE
        WHEN {$table}.is_game_upload IS NULL
        OR {$table}.is_game_upload = 0 THEN 1
        ELSE 0
    END
) AS is_game_upload_null_or_0_count,
SUM(
    CASE
        WHEN {$table}.is_game_upload = 1 THEN 1
        ELSE 0
    END
) AS is_game_upload_1_count,
SUM(
    CASE
        WHEN {$table}.is_game_upload = 2 THEN 1
        ELSE 0
    END
) AS is_game_upload_2_count,
SUM(
    CASE
        WHEN {$table}.is_game_upload = 3 THEN 1
        ELSE 0
    END
) AS is_game_upload_3_count,
SUM(
    CASE
        WHEN {$initTable}.is_root = 1 THEN 1
        ELSE 0
    END
) AS is_root_count,
SUM(
    CASE
        WHEN {$initTable}.is_root = 0 THEN 1
        ELSE 0
    END
) AS is_not_root_count
SELECT;
        return $builder->selectRaw($selectRaw)->firstFromSR();
    }
}
